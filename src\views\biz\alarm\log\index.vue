<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="报警名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入报警名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警对象" prop="objName">
        <el-input
          v-model="queryParams.objName"
          placeholder="请输入报警对象名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警点位" prop="pointName">
        <el-input
          v-model="queryParams.pointName"
          placeholder="请输入报警点位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警时间" prop="alarmTime">
        <el-date-picker clearable
                        v-model="queryParams.alarmTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择报警时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否处理" prop="handleFlag">
        <el-input
          v-model="queryParams.handleFlag"
          placeholder="请输入是否处理(1:是;0否)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:alarmLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:alarmLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="alarmLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报警名称" align="center" prop="name" />
      <el-table-column label="报警对象名称" align="center" prop="objName" />
      <el-table-column label="报警点位名称" align="center" prop="pointName" />
      <el-table-column label="报警值" align="center" prop="alarmValue" />
      <el-table-column label="报警时间" align="center" prop="alarmTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.alarmTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否处理(1:是;0否)" align="center" prop="handleFlag" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:alarmLog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listAlarmLog, delAlarmLog } from "@/api/biz/alarmLog";

export default {
  name: "AlarmLog",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报警日志表格数据
      alarmLogList: [],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        alarmRuleId: undefined,
        name: undefined,
        objId: undefined,
        objName: undefined,
        pointId: undefined,
        pointName: undefined,
        alarmValue: undefined,
        alarmTime: undefined,
        handleFlag: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询报警日志列表 */
    getList() {
      this.loading = true;
      listAlarmLog(this.queryParams).then(response => {
        this.alarmLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除报警日志编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delAlarmLog(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/alarmLog/export', {
        ...this.queryParams
      }, `alarmLog_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
